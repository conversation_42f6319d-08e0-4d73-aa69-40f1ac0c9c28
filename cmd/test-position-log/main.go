package main

import (
	"encoding/json"
	"log"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
	"yotracker/migrations"
)

func main() {
	log.Println("Testing Position Log Report...")

	config.InitDB()
	migrations.Migrate()

	reportService := service.NewReportService()
	
	// Test with recent data
	endDate := time.Now()
	startDate := endDate.Add(-24 * time.Hour) // Last 24 hours
	
	filters := models.ReportFilters{
		StartDate: &startDate,
		EndDate:   &endDate,
		PerPage:   10, // Limit to 10 records for demo
	}

	// Get the position log report ID
	var report models.Report
	err := config.DB.Where("report_type = ?", "position_log").First(&report).Error
	if err != nil {
		log.Fatalf("Position log report not found: %v", err)
	}

	log.Printf("Found Position Log Report: ID %d - %s", report.Id, report.Name)

	// Generate the report
	reportData, err := reportService.GenerateReport(report.Id, filters, "json")
	if err != nil {
		log.Fatalf("Error generating position log report: %v", err)
	}

	log.Printf("✅ Position Log Report Generated Successfully!")
	log.Printf("   • Report: %s", reportData.ReportInfo.Name)
	log.Printf("   • Generated in: %s", reportData.Metadata.ExecutionTime)
	log.Printf("   • Total GPS records: %d", reportData.Metadata.TotalRecords)
	log.Printf("   • Filtered records: %d", reportData.Metadata.FilteredRecords)

	// Show sample data
	if dataSlice, ok := reportData.Data.([]interface{}); ok && len(dataSlice) > 0 {
		log.Printf("\n📍 Sample GPS Position Data:")
		
		// Convert to JSON for pretty printing
		sampleData := dataSlice[:min(3, len(dataSlice))]
		for i, record := range sampleData {
			recordJSON, _ := json.MarshalIndent(record, "   ", "  ")
			log.Printf("   %d. %s", i+1, recordJSON)
		}
	}

	// Test with device filter
	log.Println("\n🚗 Testing with device filter...")
	filters.ClientDeviceIds = []uint{3} // Filter by device 3
	filters.PerPage = 5

	reportData, err = reportService.GenerateReport(report.Id, filters, "json")
	if err != nil {
		log.Printf("Error with device filter: %v", err)
	} else {
		log.Printf("✅ Device 3 Position Log: %d records in %s", 
			reportData.Metadata.FilteredRecords, reportData.Metadata.ExecutionTime)
	}

	log.Println("\n🎉 Position Log Report working perfectly!")
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
