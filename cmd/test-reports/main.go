package main

import (
	"encoding/json"
	"log"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
	"yotracker/migrations"
)

func main() {
	log.Println("Testing flexible reporting system...")

	// Initialize database
	config.InitDB()
	migrations.Migrate()

	// Create report service
	reportService := service.NewReportService()

	// Test powerful reports
	testDriverSafetyScorecard(reportService)
	testSpeedingViolationsReport(reportService)
	testFleetROIDashboard(reportService)
	testExecutiveFleetSummary(reportService)
}

func testDriverSafetyScorecard(reportService *service.ReportService) {
	log.Println("\n=== TESTING DRIVER SAFETY SCORECARD ===")

	startDate := time.Date(2025, 8, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Now()

	filters := models.ReportFilters{
		StartDate: &startDate,
		EndDate:   &endDate,
	}

	// Generate report (report ID 6 = Driver Safety Scorecard)
	reportData, err := reportService.GenerateReport(6, filters, "json")
	if err != nil {
		log.Printf("Error generating driver safety scorecard: %v", err)
		return
	}

	log.Printf("Report: %s", reportData.ReportInfo.Name)
	log.Printf("Generated in: %s", reportData.Metadata.ExecutionTime)

	if summary := reportData.Summary; summary != nil {
		summaryJSON, _ := json.MarshalIndent(summary, "", "  ")
		log.Printf("Safety Summary:\n%s", summaryJSON)
	}
}

func testSpeedingViolationsReport(reportService *service.ReportService) {
	log.Println("\n=== TESTING SPEEDING VIOLATIONS REPORT ===")

	startDate := time.Date(2025, 8, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Now()

	filters := models.ReportFilters{
		StartDate: &startDate,
		EndDate:   &endDate,
		PerPage:   5,
	}

	// Generate report (report ID 7 = Speeding Violations)
	reportData, err := reportService.GenerateReport(7, filters, "json")
	if err != nil {
		log.Printf("Error generating speeding violations report: %v", err)
		return
	}

	log.Printf("Report: %s", reportData.ReportInfo.Name)
	log.Printf("Total violations: %d", reportData.Metadata.FilteredRecords)
	log.Printf("Generated in: %s", reportData.Metadata.ExecutionTime)
}

func testFleetROIDashboard(reportService *service.ReportService) {
	log.Println("\n=== TESTING FLEET ROI DASHBOARD ===")

	startDate := time.Date(2025, 8, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Now()

	filters := models.ReportFilters{
		StartDate: &startDate,
		EndDate:   &endDate,
	}

	// Generate report (report ID 9 = Fleet ROI Dashboard)
	reportData, err := reportService.GenerateReport(9, filters, "json")
	if err != nil {
		log.Printf("Error generating fleet ROI dashboard: %v", err)
		return
	}

	log.Printf("Report: %s", reportData.ReportInfo.Name)
	if summary := reportData.Summary; summary != nil {
		summaryJSON, _ := json.MarshalIndent(summary, "", "  ")
		log.Printf("ROI Summary:\n%s", summaryJSON)
	}
}

func testExecutiveFleetSummary(reportService *service.ReportService) {
	log.Println("\n=== TESTING EXECUTIVE FLEET SUMMARY ===")

	startDate := time.Date(2025, 8, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Now()

	filters := models.ReportFilters{
		StartDate: &startDate,
		EndDate:   &endDate,
	}

	// Generate report (report ID 10 = Executive Fleet Summary)
	reportData, err := reportService.GenerateReport(10, filters, "json")
	if err != nil {
		log.Printf("Error generating executive fleet summary: %v", err)
		return
	}

	log.Printf("Report: %s", reportData.ReportInfo.Name)
	if summary := reportData.Summary; summary != nil {
		summaryJSON, _ := json.MarshalIndent(summary, "", "  ")
		log.Printf("Executive Summary:\n%s", summaryJSON)
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
