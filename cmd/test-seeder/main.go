package main

import (
	"log"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/seed"
	"yotracker/migrations"
)

func main() {
	log.Println("Testing cleaned up reports seeder...")
	
	config.InitDB()
	migrations.Migrate()
	
	// Clear existing reports
	config.DB.Where("1 = 1").Delete(&models.Report{})
	
	// Run seeder
	seed.Seed()
	
	// Count reports
	var count int64
	config.DB.Model(&models.Report{}).Count(&count)
	
	log.Printf("✅ Seeding completed! Total reports: %d", count)
	
	// Show sample reports by category
	var reports []models.Report
	config.DB.Order("category, name").Find(&reports)
	
	currentCategory := ""
	for _, report := range reports {
		if report.Category != currentCategory {
			currentCategory = report.Category
			log.Printf("\n📂 %s Reports:", currentCategory)
		}
		log.Printf("   • %s", report.Name)
	}
	
	log.Println("\n🎉 No duplicate errors - seeder is clean!")
}
