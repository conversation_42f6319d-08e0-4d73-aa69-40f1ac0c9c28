package main

import (
	"bytes"
	"encoding/json"
	"io"
	"log"
	"net/http"
	"time"
	"yotracker/config"
	"yotracker/migrations"
)

func main() {
	log.Println("Testing Report API Routes...")

	// Initialize database
	config.InitDB()
	migrations.Migrate()

	// Test frontend routes
	testFrontendReportRoutes()
	
	// Test backend routes
	testBackendReportRoutes()
}

func testFrontendReportRoutes() {
	log.Println("\n=== TESTING FRONTEND REPORT ROUTES ===")
	
	baseURL := "http://localhost:8081/api/v1/reports"
	
	// Test 1: Get reports list
	log.Println("1. Testing GET /api/v1/reports")
	resp, err := http.Get(baseURL)
	if err != nil {
		log.Printf("❌ Error: %v", err)
	} else {
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		log.Printf("✅ Status: %d, Response length: %d bytes", resp.<PERSON>, len(body))
		
		// Parse and show sample
		var result map[string]interface{}
		if json.Unmarshal(body, &result) == nil {
			if data, ok := result["data"].(map[string]interface{}); ok {
				if totalReports, ok := data["total_reports"].(float64); ok {
					log.Printf("   📊 Total reports available: %.0f", totalReports)
				}
			}
		}
	}

	// Test 2: Get specific report details
	log.Println("2. Testing GET /api/v1/reports/1")
	resp, err = http.Get(baseURL + "/1")
	if err != nil {
		log.Printf("❌ Error: %v", err)
	} else {
		defer resp.Body.Close()
		log.Printf("✅ Status: %d", resp.StatusCode)
	}

	// Test 3: Generate report
	log.Println("3. Testing POST /api/v1/reports/1/generate")
	filters := map[string]interface{}{
		"start_date": time.Now().AddDate(0, 0, -7).Format(time.RFC3339),
		"end_date":   time.Now().Format(time.RFC3339),
		"per_page":   10,
	}
	filtersJSON, _ := json.Marshal(filters)
	
	resp, err = http.Post(baseURL+"/1/generate", "application/json", bytes.NewBuffer(filtersJSON))
	if err != nil {
		log.Printf("❌ Error: %v", err)
	} else {
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		log.Printf("✅ Status: %d, Response length: %d bytes", resp.StatusCode, len(body))
	}

	// Test 4: Get scheduled reports
	log.Println("4. Testing GET /api/v1/reports/scheduled")
	resp, err = http.Get(baseURL + "/scheduled")
	if err != nil {
		log.Printf("❌ Error: %v", err)
	} else {
		defer resp.Body.Close()
		log.Printf("✅ Status: %d", resp.StatusCode)
	}
}

func testBackendReportRoutes() {
	log.Println("\n=== TESTING BACKEND REPORT ROUTES ===")
	
	baseURL := "http://localhost:8080/api/v1/reports"
	
	// Test 1: Get all reports (admin)
	log.Println("1. Testing GET /api/v1/reports (backend)")
	resp, err := http.Get(baseURL)
	if err != nil {
		log.Printf("❌ Error: %v", err)
	} else {
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		log.Printf("✅ Status: %d, Response length: %d bytes", resp.StatusCode, len(body))
	}

	// Test 2: Get report categories
	log.Println("2. Testing GET /api/v1/reports/categories")
	resp, err = http.Get(baseURL + "/categories")
	if err != nil {
		log.Printf("❌ Error: %v", err)
	} else {
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		log.Printf("✅ Status: %d, Response length: %d bytes", resp.StatusCode, len(body))
		
		// Parse and show categories
		var result map[string]interface{}
		if json.Unmarshal(body, &result) == nil {
			if data, ok := result["data"].([]interface{}); ok {
				log.Printf("   📂 Available categories: %v", data)
			}
		}
	}

	// Test 3: Get report stats
	log.Println("3. Testing GET /api/v1/reports/stats")
	resp, err = http.Get(baseURL + "/stats")
	if err != nil {
		log.Printf("❌ Error: %v", err)
	} else {
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		log.Printf("✅ Status: %d, Response length: %d bytes", resp.StatusCode, len(body))
		
		// Parse and show stats
		var result map[string]interface{}
		if json.Unmarshal(body, &result) == nil {
			if data, ok := result["data"].(map[string]interface{}); ok {
				log.Printf("   📈 Report Statistics:")
				for key, value := range data {
					log.Printf("      %s: %v", key, value)
				}
			}
		}
	}

	// Test 4: Get scheduled reports (admin)
	log.Println("4. Testing GET /api/v1/reports/scheduled (backend)")
	resp, err = http.Get(baseURL + "/scheduled")
	if err != nil {
		log.Printf("❌ Error: %v", err)
	} else {
		defer resp.Body.Close()
		log.Printf("✅ Status: %d", resp.StatusCode)
	}
}

func makeRequest(method, url string, body []byte) (*http.Response, error) {
	client := &http.Client{Timeout: 10 * time.Second}
	
	var req *http.Request
	var err error
	
	if body != nil {
		req, err = http.NewRequest(method, url, bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
	} else {
		req, err = http.NewRequest(method, url, nil)
	}
	
	if err != nil {
		return nil, err
	}
	
	return client.Do(req)
}
