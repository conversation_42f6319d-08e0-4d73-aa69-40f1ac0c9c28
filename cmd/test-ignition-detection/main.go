package main

import (
	"log"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/migrations"
)

func main() {
	log.Println("Testing ignition-aware trip detection...")

	// Initialize database
	config.InitDB()
	migrations.Migrate()

	// Create sample GPS data with ignition status to demonstrate the concept
	createSampleIgnitionData()

	log.Println("Sample ignition data created. Once you start capturing real ignition status,")
	log.Println("the trip detection will automatically use it for more accurate idle time detection.")
}

func createSampleIgnitionData() {
	// Clean up any existing test data
	config.DB.Where("client_device_id = ?", 999).Delete(&models.GPSData{})

	baseTime := time.Now().Add(-2 * time.Hour)
	
	// Sample trip scenario: Vehicle starts, drives, stops at traffic light, continues, then parks
	sampleData := []struct {
		offsetMinutes int
		latitude      float64
		longitude     float64
		speed         float64
		ignition      bool
		description   string
	}{
		{0, -17.8252, 31.0335, 0, false, "Parked - ignition off"},
		{1, -17.8252, 31.0335, 0, false, "Still parked"},
		{2, -17.8252, 31.0335, 0, true, "Ignition ON - trip starts!"},
		{3, -17.8250, 31.0340, 15, true, "Driving"},
		{4, -17.8245, 31.0350, 25, true, "Driving faster"},
		{5, -17.8240, 31.0360, 0, true, "Stopped at traffic light - ignition still on"},
		{6, -17.8240, 31.0360, 0, true, "Still at traffic light"},
		{7, -17.8235, 31.0370, 20, true, "Moving again"},
		{8, -17.8230, 31.0380, 30, true, "Driving"},
		{9, -17.8225, 31.0390, 0, true, "Arriving at destination"},
		{10, -17.8225, 31.0390, 0, false, "Ignition OFF - trip ends!"},
		{11, -17.8225, 31.0390, 0, false, "Parked"},
	}

	log.Println("Creating sample GPS data with ignition status:")
	
	for _, data := range sampleData {
		timestamp := baseTime.Add(time.Duration(data.offsetMinutes) * time.Minute)
		
		gpsData := models.GPSData{
			ClientDeviceId: uintPtr(999), // Test device
			GPSTimestamp:   &timestamp,
			Latitude:       data.latitude,
			Longitude:      data.longitude,
			Speed:          &data.speed,
			IgnitionStatus: &data.ignition,
		}
		
		config.DB.Create(&gpsData)
		
		ignitionStatus := "OFF"
		if data.ignition {
			ignitionStatus = "ON"
		}
		
		log.Printf("  %s: %.4f,%.4f speed=%.0f ignition=%s - %s", 
			timestamp.Format("15:04"), data.latitude, data.longitude, 
			data.speed, ignitionStatus, data.description)
	}
	
	log.Println("\nWith ignition status, trip detection will:")
	log.Println("✅ Start trip immediately when ignition turns ON (more reliable than speed)")
	log.Println("✅ End trip immediately when ignition turns OFF (no waiting for idle timeout)")
	log.Println("✅ Calculate accurate idle time during trips (ignition ON but speed = 0)")
	log.Println("✅ Ignore GPS drift when vehicle is parked (ignition OFF)")
	
	log.Println("\nBenefits over speed-based detection:")
	log.Println("• No false trips from GPS drift when parked")
	log.Println("• Immediate trip start/end detection")
	log.Println("• Accurate idle time at traffic lights, drive-throughs, etc.")
	log.Println("• Better handling of slow-speed maneuvers (parking, loading)")
}

func uintPtr(u uint) *uint {
	return &u
}
