package main

import (
	"log"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
	"yotracker/migrations"
)

func main() {
	log.Println("Testing final powerful reports setup...")

	config.InitDB()
	migrations.Migrate()

	reportService := service.NewReportService()
	startDate := time.Date(2025, 8, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Now()
	filters := models.ReportFilters{
		StartDate: &startDate,
		EndDate:   &endDate,
	}

	// Test Trip Detail Report (ID 23)
	reportData, err := reportService.GenerateReport(23, filters, "json")
	if err != nil {
		log.Printf("Error generating Trip Detail Report: %v", err)
	} else {
		log.Printf("✅ Trip Detail Report: %d records in %s", 
			reportData.Metadata.FilteredRecords, reportData.Metadata.ExecutionTime)
	}

	// Test Driver Safety Scorecard (ID 14)
	reportData, err = reportService.GenerateReport(14, filters, "json")
	if err != nil {
		log.Printf("Error generating Driver Safety Scorecard: %v", err)
	} else {
		log.Printf("✅ Driver Safety Scorecard: %d records in %s", 
			reportData.Metadata.FilteredRecords, reportData.Metadata.ExecutionTime)
	}

	// Show available reports
	var reports []models.Report
	config.DB.Order("category, name").Find(&reports)
	
	log.Printf("\n📊 Available Powerful Reports (%d total):", len(reports))
	currentCategory := ""
	for _, report := range reports {
		if report.Category != currentCategory {
			currentCategory = report.Category
			log.Printf("\n=== %s Reports ===", currentCategory)
		}
		log.Printf("  • %s", report.Name)
	}
	
	log.Println("\n🎉 Powerful reporting system ready for production!")
}
