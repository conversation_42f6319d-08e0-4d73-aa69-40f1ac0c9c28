package main

import (
	"fmt"
	"log"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
	"yotracker/migrations"
)

func main() {
	log.Println("Testing driving behavior detection on existing trips...")

	// Initialize database
	config.InitDB()
	migrations.Migrate()

	// Get a few recent trips to test behavior detection
	var trips []models.Trip
	err := config.DB.Where("start_time >= ?", "2025-08-01").
		Order("distance DESC").
		Limit(3).
		Find(&trips).Error
	
	if err != nil {
		log.Fatalf("Failed to fetch trips: %v", err)
	}

	if len(trips) == 0 {
		log.Println("No trips found to analyze")
		return
	}

	log.Printf("Found %d trips to analyze for driving behavior", len(trips))

	// Create driving behavior service
	behaviorService := service.NewDrivingBehaviorService()

	// Process each trip
	for _, trip := range trips {
		log.Printf("Analyzing trip %d: %.2fkm, device %d", trip.Id, trip.Distance, trip.ClientDeviceId)
		
		err := behaviorService.ProcessTripBehavior(trip)
		if err != nil {
			log.Printf("Error processing trip %d: %v", trip.Id, err)
		} else {
			log.Printf("Successfully processed trip %d", trip.Id)
		}
	}

	// Show results
	showBehaviorResults()
}

func showBehaviorResults() {
	log.Println("\n=== DRIVING BEHAVIOR ANALYSIS RESULTS ===")
	
	// Count events by type
	var eventCounts []struct {
		EventType string `json:"event_type"`
		Count     int    `json:"count"`
		AvgSeverity float64 `json:"avg_severity"`
	}
	
	config.DB.Raw(`
		SELECT 
			event_type,
			COUNT(*) as count,
			AVG(COALESCE(severity, 0)) as avg_severity
		FROM driving_behavior_events 
		GROUP BY event_type
		ORDER BY count DESC
	`).Scan(&eventCounts)
	
	if len(eventCounts) == 0 {
		log.Println("No driving behavior events detected")
		return
	}
	
	log.Println("Events detected:")
	for _, event := range eventCounts {
		log.Printf("  %s: %d events (avg severity: %.1f)", event.EventType, event.Count, event.AvgSeverity)
	}
	
	// Show sample events
	var sampleEvents []models.DrivingBehaviorEvent
	config.DB.Order("severity DESC").Limit(5).Find(&sampleEvents)
	
	log.Println("\nTop 5 most severe events:")
	for i, event := range sampleEvents {
		severity := "Unknown"
		if event.Severity != nil {
			if *event.Severity >= 8 {
				severity = "Critical"
			} else if *event.Severity >= 5 {
				severity = "High"
			} else if *event.Severity >= 3 {
				severity = "Medium"
			} else {
				severity = "Low"
			}
		}
		
		speed := "N/A"
		if event.Speed != nil {
			speed = fmt.Sprintf("%.0f km/h", *event.Speed)
		}
		
		log.Printf("%d. %s - %s severity, Speed: %s, Trip: %v", 
			i+1, event.EventType, severity, speed, event.TripId)
	}
}
