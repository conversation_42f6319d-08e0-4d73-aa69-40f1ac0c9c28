package main

import (
	"log"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
	"yotracker/migrations"
)

func main() {
	log.Println("🧪 Report Data Validation Tests")
	log.Println("===============================")

	// Initialize database
	config.InitDB()
	migrations.Migrate()

	reportService := service.NewReportService()
	
	// Test each report type with validation
	testPositionLogReport(reportService)
	testTripDetailReport(reportService)
	testDriverSafetyReport(reportService)
	testSpeedingViolationsReport(reportService)
	testFilterValidation(reportService)
	testErrorHandling(reportService)
	
	log.Println("\n✅ All validation tests completed!")
}

func testPositionLogReport(reportService *service.ReportService) {
	log.Println("\n📍 Testing Position Log Report Data Validation")
	
	filters := models.ReportFilters{
		StartDate: timePtr(time.Now().Add(-24 * time.Hour)),
		EndDate:   timePtr(time.Now()),
		PerPage:   5,
	}

	// Find position log report
	var report models.Report
	err := config.DB.Where("report_type = ?", "position_log").First(&report).Error
	if err != nil {
		log.Printf("❌ Position log report not found: %v", err)
		return
	}

	reportData, err := reportService.GenerateReport(report.Id, filters, "json")
	if err != nil {
		log.Printf("❌ Failed to generate position log: %v", err)
		return
	}

	log.Printf("✅ Position Log Report Generated")
	log.Printf("   Records: %d total, %d filtered", 
		reportData.Metadata.TotalRecords, reportData.Metadata.FilteredRecords)
	log.Printf("   Execution time: %s", reportData.Metadata.ExecutionTime)

	// Validate data structure
	if data, ok := reportData.Data.([]interface{}); ok {
		log.Printf("   Data type: Array with %d records", len(data))
		
		if len(data) > 0 {
			// Check first record structure
			if record, ok := data[0].(map[string]interface{}); ok {
				requiredFields := []string{"gps_timestamp", "latitude", "longitude", "client_device_id"}
				for _, field := range requiredFields {
					if _, exists := record[field]; exists {
						log.Printf("   ✅ Has required field: %s", field)
					} else {
						log.Printf("   ❌ Missing required field: %s", field)
					}
				}
			}
		}
	} else {
		log.Printf("   ❌ Unexpected data type: %T", reportData.Data)
	}
}

func testTripDetailReport(reportService *service.ReportService) {
	log.Println("\n🚗 Testing Trip Detail Report Data Validation")
	
	filters := models.ReportFilters{
		StartDate: timePtr(time.Now().Add(-30 * 24 * time.Hour)),
		EndDate:   timePtr(time.Now()),
		PerPage:   5,
	}

	// Find trip detail report
	var report models.Report
	err := config.DB.Where("report_type = ?", "trip_detail").First(&report).Error
	if err != nil {
		log.Printf("❌ Trip detail report not found: %v", err)
		return
	}

	reportData, err := reportService.GenerateReport(report.Id, filters, "json")
	if err != nil {
		log.Printf("❌ Failed to generate trip detail: %v", err)
		return
	}

	log.Printf("✅ Trip Detail Report Generated")
	log.Printf("   Records: %d total, %d filtered", 
		reportData.Metadata.TotalRecords, reportData.Metadata.FilteredRecords)

	// Validate trip data
	if data, ok := reportData.Data.([]models.Trip); ok {
		log.Printf("   Data type: Trip array with %d records", len(data))
		
		for i, trip := range data {
			if i >= 3 { // Check first 3 trips
				break
			}
			
			log.Printf("   Trip %d validation:", i+1)
			
			// Validate required fields
			if trip.Distance > 0 {
				log.Printf("     ✅ Has distance: %.2f km", trip.Distance)
			} else {
				log.Printf("     ❌ Invalid distance: %.2f", trip.Distance)
			}
			
			if trip.Duration != nil && *trip.Duration > 0 {
				log.Printf("     ✅ Has duration: %d seconds", *trip.Duration)
			} else {
				log.Printf("     ❌ Invalid duration")
			}
			
			if !trip.StartTime.IsZero() && trip.EndTime != nil && !trip.EndTime.IsZero() {
				if trip.StartTime.Before(*trip.EndTime) {
					log.Printf("     ✅ Valid time range")
				} else {
					log.Printf("     ❌ Invalid time range: start after end")
				}
			} else {
				log.Printf("     ❌ Missing timestamps")
			}
		}
	} else {
		log.Printf("   ❌ Unexpected data type: %T", reportData.Data)
	}
}

func testDriverSafetyReport(reportService *service.ReportService) {
	log.Println("\n👨‍✈️ Testing Driver Safety Report Data Validation")
	
	filters := models.ReportFilters{
		StartDate: timePtr(time.Now().Add(-30 * 24 * time.Hour)),
		EndDate:   timePtr(time.Now()),
	}

	// Find driver safety report
	var report models.Report
	err := config.DB.Where("report_type = ?", "driver_safety_scorecard").First(&report).Error
	if err != nil {
		log.Printf("❌ Driver safety report not found: %v", err)
		return
	}

	reportData, err := reportService.GenerateReport(report.Id, filters, "json")
	if err != nil {
		log.Printf("❌ Failed to generate driver safety: %v", err)
		return
	}

	log.Printf("✅ Driver Safety Report Generated")
	log.Printf("   Records: %d filtered", reportData.Metadata.FilteredRecords)

	// Validate summary data
	if reportData.Summary != nil {
		log.Printf("   ✅ Has summary data")
		
		if summary, ok := reportData.Summary.(map[string]interface{}); ok {
			requiredSummaryFields := []string{"total_drivers", "avg_safety_score"}
			for _, field := range requiredSummaryFields {
				if _, exists := summary[field]; exists {
					log.Printf("     ✅ Summary has: %s = %v", field, summary[field])
				} else {
					log.Printf("     ❌ Summary missing: %s", field)
				}
			}
		}
	} else {
		log.Printf("   ❌ Missing summary data")
	}

	// Validate driver data
	if data, ok := reportData.Data.([]interface{}); ok {
		log.Printf("   Data type: Array with %d driver records", len(data))
		
		for i, record := range data {
			if i >= 2 { // Check first 2 drivers
				break
			}
			
			if driverRecord, ok := record.(map[string]interface{}); ok {
				log.Printf("   Driver %d validation:", i+1)
				
				requiredFields := []string{"safety_score", "total_trips", "risk_level"}
				for _, field := range requiredFields {
					if value, exists := driverRecord[field]; exists {
						log.Printf("     ✅ Has %s: %v", field, value)
					} else {
						log.Printf("     ❌ Missing %s", field)
					}
				}
			}
		}
	}
}

func testSpeedingViolationsReport(reportService *service.ReportService) {
	log.Println("\n🚨 Testing Speeding Violations Report Data Validation")
	
	filters := models.ReportFilters{
		StartDate: timePtr(time.Now().Add(-30 * 24 * time.Hour)),
		EndDate:   timePtr(time.Now()),
		PerPage:   10,
	}

	// Find speeding violations report
	var report models.Report
	err := config.DB.Where("report_type = ?", "speeding_violations").First(&report).Error
	if err != nil {
		log.Printf("❌ Speeding violations report not found: %v", err)
		return
	}

	reportData, err := reportService.GenerateReport(report.Id, filters, "json")
	if err != nil {
		log.Printf("❌ Failed to generate speeding violations: %v", err)
		return
	}

	log.Printf("✅ Speeding Violations Report Generated")
	log.Printf("   Records: %d total, %d filtered", 
		reportData.Metadata.TotalRecords, reportData.Metadata.FilteredRecords)

	// Validate speeding events
	if data, ok := reportData.Data.([]models.DrivingBehaviorEvent); ok {
		log.Printf("   Data type: Behavior events array with %d records", len(data))
		
		for i, event := range data {
			if i >= 3 { // Check first 3 events
				break
			}
			
			log.Printf("   Event %d validation:", i+1)
			
			// Validate event type
			if event.EventType == "overspeed" {
				log.Printf("     ✅ Correct event type: %s", event.EventType)
			} else {
				log.Printf("     ❌ Wrong event type: %s", event.EventType)
			}
			
			// Validate speed data
			if event.Speed != nil && *event.Speed > 0 {
				log.Printf("     ✅ Has speed: %.1f km/h", *event.Speed)
			} else {
				log.Printf("     ❌ Invalid speed data")
			}
			
			// Validate severity
			if event.Severity != nil && *event.Severity >= 1 && *event.Severity <= 10 {
				log.Printf("     ✅ Valid severity: %.1f", *event.Severity)
			} else {
				log.Printf("     ❌ Invalid severity")
			}
		}
	} else {
		log.Printf("   ❌ Unexpected data type: %T", reportData.Data)
	}
}

func testFilterValidation(reportService *service.ReportService) {
	log.Println("\n🔍 Testing Filter Validation")
	
	// Get first report for testing
	var report models.Report
	config.DB.Where("status = ?", "active").First(&report)
	
	// Test 1: Valid date range
	log.Println("   Testing valid date range...")
	filters := models.ReportFilters{
		StartDate: timePtr(time.Now().Add(-7 * 24 * time.Hour)),
		EndDate:   timePtr(time.Now()),
		PerPage:   5,
	}
	
	_, err := reportService.GenerateReport(report.Id, filters, "json")
	if err != nil {
		log.Printf("     ❌ Valid filters failed: %v", err)
	} else {
		log.Printf("     ✅ Valid filters passed")
	}
	
	// Test 2: Invalid date range (end before start)
	log.Println("   Testing invalid date range...")
	filters = models.ReportFilters{
		StartDate: timePtr(time.Now()),
		EndDate:   timePtr(time.Now().Add(-1 * time.Hour)),
		PerPage:   5,
	}
	
	reportData, err := reportService.GenerateReport(report.Id, filters, "json")
	if err != nil {
		log.Printf("     ❌ Invalid filters caused error: %v", err)
	} else {
		log.Printf("     ✅ Invalid filters handled gracefully: %d records", 
			reportData.Metadata.FilteredRecords)
	}
	
	// Test 3: Device filtering
	log.Println("   Testing device filtering...")
	filters = models.ReportFilters{
		StartDate:       timePtr(time.Now().Add(-7 * 24 * time.Hour)),
		EndDate:         timePtr(time.Now()),
		ClientDeviceIds: []uint{1, 2},
		PerPage:         5,
	}
	
	reportData, err = reportService.GenerateReport(report.Id, filters, "json")
	if err != nil {
		log.Printf("     ❌ Device filtering failed: %v", err)
	} else {
		log.Printf("     ✅ Device filtering passed: %d records", 
			reportData.Metadata.FilteredRecords)
	}
}

func testErrorHandling(reportService *service.ReportService) {
	log.Println("\n⚠️ Testing Error Handling")
	
	// Test 1: Invalid report ID
	log.Println("   Testing invalid report ID...")
	filters := models.ReportFilters{}
	_, err := reportService.GenerateReport(99999, filters, "json")
	if err != nil {
		log.Printf("     ✅ Invalid report ID properly rejected: %v", err)
	} else {
		log.Printf("     ❌ Invalid report ID should have failed")
	}
	
	// Test 2: Empty filters (should use defaults)
	log.Println("   Testing empty filters...")
	var report models.Report
	config.DB.Where("status = ?", "active").First(&report)
	
	_, err = reportService.GenerateReport(report.Id, models.ReportFilters{}, "json")
	if err != nil {
		log.Printf("     ❌ Empty filters failed: %v", err)
	} else {
		log.Printf("     ✅ Empty filters handled with defaults")
	}
}

func timePtr(t time.Time) *time.Time {
	return &t
}
