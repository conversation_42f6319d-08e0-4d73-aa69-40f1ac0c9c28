package main

import (
	"log"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
	"yotracker/migrations"
)

func main() {
	log.Println("🚀 Testing Report Export Functionality...")

	config.InitDB()
	migrations.Migrate()

	// Test export service
	exportService := service.NewReportExportService()
	reportService := service.NewReportService()

	// Create test filters
	startDate := time.Date(2025, 8, 1, 0, 0, 0, 0, time.UTC)
	endDate := time.Now()
	filters := models.ReportFilters{
		StartDate: &startDate,
		EndDate:   &endDate,
		PerPage:   10,
	}

	// Test with a real report (Position Log Report - ID 32)
	log.Println("\n📊 Testing Position Log Report Export...")
	reportData, err := reportService.GenerateReport(32, filters, "json")
	if err != nil {
		log.Printf("❌ Error generating report: %v", err)
		return
	}

	log.Printf("✅ Report generated: %s", reportData.ReportInfo.Name)
	log.Printf("   Records: %d total, %d filtered", reportData.Metadata.TotalRecords, reportData.Metadata.FilteredRecords)
	log.Printf("   Execution time: %s", reportData.Metadata.ExecutionTime)

	// Test PDF Export
	log.Println("\n📄 Testing PDF Export...")
	start := time.Now()
	pdfData, contentType, err := exportService.ExportReport(reportData, "pdf")
	pdfDuration := time.Since(start)
	
	if err != nil {
		log.Printf("❌ PDF export failed: %v", err)
	} else {
		log.Printf("✅ PDF export successful!")
		log.Printf("   Size: %d bytes", len(pdfData))
		log.Printf("   Content-Type: %s", contentType)
		log.Printf("   Generation time: %v", pdfDuration)
	}

	// Test Excel Export
	log.Println("\n📊 Testing Excel Export...")
	start = time.Now()
	excelData, contentType, err := exportService.ExportReport(reportData, "excel")
	excelDuration := time.Since(start)
	
	if err != nil {
		log.Printf("❌ Excel export failed: %v", err)
	} else {
		log.Printf("✅ Excel export successful!")
		log.Printf("   Size: %d bytes", len(excelData))
		log.Printf("   Content-Type: %s", contentType)
		log.Printf("   Generation time: %v", excelDuration)
	}

	// Test CSV Export
	log.Println("\n📋 Testing CSV Export...")
	start = time.Now()
	csvData, contentType, err := exportService.ExportReport(reportData, "csv")
	csvDuration := time.Since(start)
	
	if err != nil {
		log.Printf("❌ CSV export failed: %v", err)
	} else {
		log.Printf("✅ CSV export successful!")
		log.Printf("   Size: %d bytes", len(csvData))
		log.Printf("   Content-Type: %s", contentType)
		log.Printf("   Generation time: %v", csvDuration)
		
		// Show sample CSV content
		csvString := string(csvData)
		if len(csvString) > 200 {
			log.Printf("   Sample content: %s...", csvString[:200])
		} else {
			log.Printf("   Content: %s", csvString)
		}
	}

	// Test with Trip Detail Report if available
	log.Println("\n🚗 Testing Trip Detail Report Export...")
	tripReportData, err := reportService.GenerateReport(33, filters, "json") // Trip Detail Report ID
	if err != nil {
		log.Printf("⚠️  Trip report not available: %v", err)
	} else {
		log.Printf("✅ Trip report generated: %d records", tripReportData.Metadata.FilteredRecords)
		
		// Test CSV export for trips
		csvData, _, err := exportService.ExportReport(tripReportData, "csv")
		if err != nil {
			log.Printf("❌ Trip CSV export failed: %v", err)
		} else {
			log.Printf("✅ Trip CSV export successful: %d bytes", len(csvData))
		}
	}

	// Performance Summary
	log.Println("\n⚡ Performance Summary:")
	log.Printf("   PDF Generation: %v", pdfDuration)
	log.Printf("   Excel Generation: %v", excelDuration)
	log.Printf("   CSV Generation: %v", csvDuration)

	// Test error handling
	log.Println("\n🧪 Testing Error Handling...")
	_, _, err = exportService.ExportReport(reportData, "invalid_format")
	if err != nil {
		log.Printf("✅ Error handling works: %v", err)
	} else {
		log.Printf("❌ Error handling failed - should have returned error")
	}

	log.Println("\n🎉 Export functionality testing completed!")
}
