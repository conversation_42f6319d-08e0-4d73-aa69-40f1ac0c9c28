package main

import (
	"log"
	"yotracker/config"
	"yotracker/internal/service"
	"yotracker/migrations"
)

func main() {
	log.Println("Debug: Testing trip detection on device 3...")

	// Initialize database
	config.InitDB()
	migrations.Migrate()

	// Create trip detection service
	tripDetectionService := service.NewTripDetectionService()

	// Process only device 3 data from today
	log.Println("Processing device 3 GPS data...")
	err := tripDetectionService.ProcessUnassignedGPSData()
	if err != nil {
		log.Printf("Error processing device 3: %v", err)
	} else {
		log.Println("Device 3 processing completed")
	}
}
