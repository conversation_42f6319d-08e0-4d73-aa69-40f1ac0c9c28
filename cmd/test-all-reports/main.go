package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
	"yotracker/migrations"
)

type TestResult struct {
	ReportName      string        `json:"report_name"`
	ReportType      string        `json:"report_type"`
	Category        string        `json:"category"`
	Success         bool          `json:"success"`
	ExecutionTime   time.Duration `json:"execution_time"`
	TotalRecords    int           `json:"total_records"`
	FilteredRecords int           `json:"filtered_records"`
	HasData         bool          `json:"has_data"`
	HasSummary      bool          `json:"has_summary"`
	ErrorMessage    string        `json:"error_message,omitempty"`
	DataSample      interface{}   `json:"data_sample,omitempty"`
}

func main() {
	log.Println("🧪 Comprehensive Report Testing Suite")
	log.Println("=====================================")

	// Initialize database
	config.InitDB()
	migrations.Migrate()

	// Get all active reports
	var reports []models.Report
	err := config.DB.Where("status = ?", "active").
		Order("category, name").
		Find(&reports).Error
	
	if err != nil {
		log.Fatalf("Failed to fetch reports: %v", err)
	}

	log.Printf("📊 Testing %d reports...\n", len(reports))

	// Test each report
	var results []TestResult
	reportService := service.NewReportService()
	
	// Set up test filters
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -30) // Last 30 days
	
	filters := models.ReportFilters{
		StartDate: &startDate,
		EndDate:   &endDate,
		PerPage:   10, // Small sample for testing
	}

	for _, report := range reports {
		result := testReport(reportService, report, filters)
		results = append(results, result)
		
		// Print immediate result
		status := "✅"
		if !result.Success {
			status = "❌"
		}
		
		log.Printf("%s %s (%s) - %v records in %v", 
			status, result.ReportName, result.Category, 
			result.FilteredRecords, result.ExecutionTime)
		
		if result.ErrorMessage != "" {
			log.Printf("   Error: %s", result.ErrorMessage)
		}
	}

	// Generate summary
	generateTestSummary(results)
	
	// Test specific scenarios
	testSpecificScenarios(reportService)
	
	log.Println("\n🎉 Report testing completed!")
}

func testReport(reportService *service.ReportService, report models.Report, filters models.ReportFilters) TestResult {
	result := TestResult{
		ReportName: report.Name,
		ReportType: report.ReportType,
		Category:   report.Category,
	}

	start := time.Now()
	
	// Generate report
	reportData, err := reportService.GenerateReport(report.Id, filters, "json")
	
	result.ExecutionTime = time.Since(start)
	
	if err != nil {
		result.Success = false
		result.ErrorMessage = err.Error()
		return result
	}

	result.Success = true
	result.TotalRecords = reportData.Metadata.TotalRecords
	result.FilteredRecords = reportData.Metadata.FilteredRecords
	result.HasSummary = reportData.Summary != nil

	// Check if report has data
	if reportData.Data != nil {
		switch data := reportData.Data.(type) {
		case []interface{}:
			result.HasData = len(data) > 0
			if len(data) > 0 {
				result.DataSample = data[0] // First record as sample
			}
		case []models.Trip:
			result.HasData = len(data) > 0
			if len(data) > 0 {
				result.DataSample = data[0]
			}
		case []models.DrivingBehaviorEvent:
			result.HasData = len(data) > 0
			if len(data) > 0 {
				result.DataSample = data[0]
			}
		case []models.GPSData:
			result.HasData = len(data) > 0
			if len(data) > 0 {
				result.DataSample = data[0]
			}
		default:
			result.HasData = true // Assume other types have data
		}
	}

	return result
}

func generateTestSummary(results []TestResult) {
	log.Println("\n📈 TEST SUMMARY")
	log.Println("===============")

	totalReports := len(results)
	successfulReports := 0
	reportsWithData := 0
	reportsWithSummary := 0
	totalExecutionTime := time.Duration(0)
	
	categoryStats := make(map[string]struct {
		Total     int
		Successful int
		WithData  int
	})

	for _, result := range results {
		if result.Success {
			successfulReports++
		}
		if result.HasData {
			reportsWithData++
		}
		if result.HasSummary {
			reportsWithSummary++
		}
		totalExecutionTime += result.ExecutionTime

		// Category stats
		stats := categoryStats[result.Category]
		stats.Total++
		if result.Success {
			stats.Successful++
		}
		if result.HasData {
			stats.WithData++
		}
		categoryStats[result.Category] = stats
	}

	log.Printf("📊 Overall Results:")
	log.Printf("   Total Reports: %d", totalReports)
	log.Printf("   Successful: %d (%.1f%%)", successfulReports, float64(successfulReports)/float64(totalReports)*100)
	log.Printf("   With Data: %d (%.1f%%)", reportsWithData, float64(reportsWithData)/float64(totalReports)*100)
	log.Printf("   With Summary: %d (%.1f%%)", reportsWithSummary, float64(reportsWithSummary)/float64(totalReports)*100)
	log.Printf("   Avg Execution Time: %v", totalExecutionTime/time.Duration(totalReports))

	log.Printf("\n📂 By Category:")
	for category, stats := range categoryStats {
		log.Printf("   %s: %d/%d successful, %d with data", 
			category, stats.Successful, stats.Total, stats.WithData)
	}

	// Show failed reports
	log.Printf("\n❌ Failed Reports:")
	hasFailures := false
	for _, result := range results {
		if !result.Success {
			hasFailures = true
			log.Printf("   %s: %s", result.ReportName, result.ErrorMessage)
		}
	}
	if !hasFailures {
		log.Printf("   None! All reports passed ✅")
	}

	// Show performance insights
	log.Printf("\n⚡ Performance Insights:")
	fastestReport := results[0]
	slowestReport := results[0]
	
	for _, result := range results {
		if result.Success && result.ExecutionTime < fastestReport.ExecutionTime {
			fastestReport = result
		}
		if result.Success && result.ExecutionTime > slowestReport.ExecutionTime {
			slowestReport = result
		}
	}
	
	log.Printf("   Fastest: %s (%v)", fastestReport.ReportName, fastestReport.ExecutionTime)
	log.Printf("   Slowest: %s (%v)", slowestReport.ReportName, slowestReport.ExecutionTime)
}

func testSpecificScenarios(reportService *service.ReportService) {
	log.Println("\n🎯 SPECIFIC SCENARIO TESTS")
	log.Println("==========================")

	// Test 1: Large date range
	log.Println("1. Testing large date range (90 days)...")
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -90)
	
	filters := models.ReportFilters{
		StartDate: &startDate,
		EndDate:   &endDate,
		PerPage:   1000,
	}

	start := time.Now()
	_, err := reportService.GenerateReport(1, filters, "json") // First report
	duration := time.Since(start)
	
	if err != nil {
		log.Printf("   ❌ Failed: %v", err)
	} else {
		log.Printf("   ✅ Success: %v", duration)
	}

	// Test 2: Device filtering
	log.Println("2. Testing device filtering...")
	filters = models.ReportFilters{
		StartDate:       &startDate,
		EndDate:         &endDate,
		ClientDeviceIds: []uint{1, 2},
		PerPage:         50,
	}

	start = time.Now()
	reportData, err := reportService.GenerateReport(1, filters, "json")
	duration = time.Since(start)
	
	if err != nil {
		log.Printf("   ❌ Failed: %v", err)
	} else {
		log.Printf("   ✅ Success: %d records in %v", reportData.Metadata.FilteredRecords, duration)
	}

	// Test 3: Pagination
	log.Println("3. Testing pagination...")
	filters = models.ReportFilters{
		StartDate: &startDate,
		EndDate:   &endDate,
		PerPage:   5,
		Page:      2,
	}

	start = time.Now()
	reportData, err = reportService.GenerateReport(1, filters, "json")
	duration = time.Since(start)
	
	if err != nil {
		log.Printf("   ❌ Failed: %v", err)
	} else {
		log.Printf("   ✅ Success: Page 2 with %d records in %v", reportData.Metadata.FilteredRecords, duration)
	}

	// Test 4: Empty date range
	log.Println("4. Testing empty date range...")
	futureDate := time.Now().AddDate(1, 0, 0)
	filters = models.ReportFilters{
		StartDate: &futureDate,
		EndDate:   &futureDate,
		PerPage:   10,
	}

	start = time.Now()
	reportData, err = reportService.GenerateReport(1, filters, "json")
	duration = time.Since(start)
	
	if err != nil {
		log.Printf("   ❌ Failed: %v", err)
	} else {
		log.Printf("   ✅ Success: %d records (expected 0) in %v", reportData.Metadata.FilteredRecords, duration)
	}
}

func prettyPrint(v interface{}) {
	b, _ := json.MarshalIndent(v, "", "  ")
	fmt.Println(string(b))
}
